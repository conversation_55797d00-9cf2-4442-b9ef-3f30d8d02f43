<template>
  <view class="container">
    <blcok v-for="(item, index) in packageList" :key="index">
      <view
        v-if="item.show"
        class="container-item"
        @click="handleLookDetail(item)"
      >
        <block>
          <image class="container-item-image" :src="item.url"></image>
          <view class="container-item-info">
            <text class="container-item-info-top">{{ item.name }}</text>
            <text class="container-item-info-bom" v-if="item.label">{{
              item.label
            }}</text>
          </view>
          <view class="container-item-test">
            <span v-if="item.name === '认知测试系统'">{{
              cognitiveTestingSystem.presence ? '查看报告' : '进入测试'
            }}</span>
            <span v-else-if="item.name === '步态评估'">{{
              cognitiveTestingSystem.gait ? '查看报告' : '进入测试'
            }}</span>
          </view>
        </block>
      </view>
    </blcok>

    <block v-if="list.length > 0">
      <view v-for="(item, index) in list" :key="item.Id">
        <view class="box" @click="handleToDetails(item)">
          <view class="box-top">
            <u-image
              :showLoading="true"
              :src="
                item.FillType === 1
                  ? '/static/images/lbys.png'
                  : '/static/images/lbhz.png'
              "
              width="40px"
              height="40px"
            ></u-image>
            <view class="box-top-right">
              <view class="box-top-right-top">
                <text
                  style="
                    font-size: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    width: 70%;
                  "
                >
                  {{ item.ContentName }}
                </text>
                <text style="color: #29b7a3" v-if="item.IsFill"> 已评估 </text>
                <text v-else style="color: #ff3b30"> 未评估 </text>
              </view>
            </view>
          </view>
          <view class="box-top-right-top1" v-if="item.IsFill">
            <p style="font-size: 14px; color: #666666">
              上次评估时间：{{ item.FillTime }}
            </p>
          </view>
        </view>
      </view>
    </block>
    <u-empty
      mode="data"
      icon="https://cdn.uviewui.com/uview/empty/data.png"
      v-else
    >
    </u-empty>
    <view style="height: 100px"></view>
    <u-button
      :loading="btnLoading"
      type="success"
      @click="onSeeReport"
      shape="circle"
      text="查看报告"
      customStyle="width:90%;bottom: 30px;position: fixed; left: 50%;transform: translateX(-50%);fontSize:16px;z-index:99"
    >
    </u-button>
  </view>
</template>

<script>
import { getRecommendById, riskWarningWriteGauge } from '@/api/tenant.js';
import { AssessClientEvent } from '@/utils/eventKeys.js';
import {
  checkExitReport,
  checkSyncUser,
  gaitQueryReport,
} from '@/api/supplier.js';
import { getConsultRecordInfo } from '@/api/consult.js';
import { getPatientInfoByProgramId } from '@/api/training.js';
import dayjs from 'dayjs';
const app = getApp();
export default {
  data() {
    return {
      reportId: '',
      list: [],
      btnLoading: false,
      // 来源
      // true: 问诊会话；false: 诊后会话
      isConsult: true,
      packageList: [
        {
          name: '认知测试系统',
          label: '', // 数据未采集，请进入测试
          type: 'YZCSXT',
          url: '/subRiskWarning/static/icon-head.png',
          show: false,
        },
        {
          name: '步态评估',
          label: '', // 数据未采集，请进入测试
          type: 'BTPG',
          url: '/subRiskWarning/static/icon_gait.png',
          show: true,
        },
      ],
      consultId: '',
      programId: '',
      cognitiveTestingSystem: {
        presence: false, // 脑洞
        gait: false, // 步态
      },
      isHaveProblem: false,
      startTime: '',
      endTime: '',
    };
  },
  onLoad(option) {
    this.reportId = option.reportId;
    this.isConsult = option.isConsult === 'true';
    this.consultId = option.consultId;
    this.programId = option.programId;
    this.onGetList();
    this.onGetStartAndEndTime();
    uni.$on(AssessClientEvent.updateRiskAssess, this.handleRefreshList);
  },
  methods: {
    async onGetStartAndEndTime() {
      try {
        let startTime = '';
        let endTime = '';
        let userId = '';
        if (this.consultId && this.consultId !== 'undefined') {
          const { Data } = await getConsultRecordInfo(this.consultId);
          if (Data) {
            endTime = Data.Consult.CompletedTime || new Date();
            startTime = this.$dateFormat(
              Data.Consult.CreateDate,
              'YYYY-MM-DD HH:mm:ss'
            );
            userId = Data.Consult.UserId;
          }
        }
        if (this.programId && this.programId !== 'undefined') {
          const { Data } = await getPatientInfoByProgramId({
            programId: this.programId,
          });
          if (Data) {
            userId = Data.UserInfo.Id;
            startTime = this.$dateFormat(
              Data.TrainingProgram?.StartTime,
              'YYYY-MM-DD HH:mm:ss'
            );
            endTime = Data.TrainingProgram?.ActualFinishedTime
              ? Data.TrainingProgram?.ActualFinishedTime
              : Data.TrainingProgram?.FinishedTime;
          }
        }
        this.startTime = startTime;
        this.endTime = endTime;
        // 获取脑洞报告数据
        this.onCheckSyncUser();
        // 获取步态数据
        this.onGetGaitReport();
      } catch (err) {
        console.error('出错了：', err);
      }
    },
    async onGetGaitReport() {
      const res = await gaitQueryReport({
        Phone: app.globalData.userInfo.PhoneNumber || null,
        Name: app.globalData.userInfo.Name || null,
        StartTime: this.startTime,
        EndTime: dayjs(this.endTime)
          .add(1, 'hour')
          .format('YYYY-MM-DD HH:00:00'),
      });
      if (res.Type === 200 && res.Data.Data.length) {
        this.cognitiveTestingSystem.gait = true;
      }
    },
    async onCheckSyncUser() {
      const res = await checkSyncUser({
        userId: app.globalData.userInfo.Id,
      });
      this.packageList[0].show = res.Data;
      if (res.Data) {
        // 检测是否有认知测试系统报告
        this.handleCheckIsHaveReport();
      }
    },
    handleLookDetail(item) {
      switch (item.type) {
        case 'YZCSXT':
          if (!this.cognitiveTestingSystem.presence) {
            uni.showModal({
              title: '温馨提示提示',
              content: '请打开基本认知能力测验软件完成评估',
              showCancel: false,
            });
            return;
          }
          this.onSeeReport();
          break;
        case 'BTPG':
          if (!this.cognitiveTestingSystem.gait) {
            uni.showModal({
              title: '温馨提示提示',
              content: '请请用程天康复外骨骼设备完成评估',
              showCancel: false,
            });
            return;
          }
          this.onSeeReport();
          break;
        default:
          break;
      }
    },
    // 检测是否有认知测试系统报告
    async handleCheckIsHaveReport() {
      const res = await checkExitReport({
        userId: app.globalData.userInfo.Id,
        startTime: this.startTime,
        endTime: dayjs(this.endTime)
          .add(1, 'hour')
          .format('YYYY-MM-DD HH:00:00'),
      });
      this.cognitiveTestingSystem.presence = res.Data;
    },
    handleRefreshList() {
      this.onGetList();
    },
    async onGetList() {
      uni.showLoading({
        title: '加载中',
      });
      // IsStandard 是否标准量表 IsFill 是否完成
      const res = await getRecommendById({
        ReportId: this.reportId,
        IsRecommend: this.isConsult,
        Type: 1,
      });
      uni.hideLoading();
      if (res.Type !== 200 || !res.Data) {
        uni.showModal({
          title: '提示',
          content: res.Content,
          showCancel: false,
          success: (res) => {
            uni.navigateBack();
          },
        });
        return;
      }
      res.Data.forEach((item) => {
        item.CreatedTime = item.CreatedTime
          ? this.$dateFormat(item.CreatedTime, 'YYYY-MM-DD HH:mm:ss', false)
          : '';
        item.FillTime = item.FillTime
          ? this.$dateFormat(item.FillTime, 'YYYY-MM-DD HH:mm:ss', false)
          : '';
      });
      res.Data.sort((a, b) => {
        // 1. 根据 FillType 排序：2 在前，1 在后
        if (a.FillType !== b.FillType) {
          return b.FillType - a.FillType; // FillType 2 在前
        }
        // 2. 根据 IsFill 排序：false 在前，true 在后
        if (a.IsFill !== b.IsFill) {
          return a.IsFill ? 1 : -1; // IsFill false 在前
        }
        // 3. 如果 IsFill 为 true，按 CreatedTime 排序
        if (a.IsFill && b.IsFill) {
          const dateA = new Date(a.FillTime);
          const dateB = new Date(b.FillTime);
          return dateB - dateA; // 时间从大到小排序
        }
        return 0; // 如果前两项相等，保持顺序
      });
      // FillType  2 患者做  1 医生做
      this.list = res.Data;
    },
    handleToDetails(item) {
      if (item.FillType === 1 && !item.IsFill) {
        uni.showToast({
          title: '该量表是医生填写，您无需填写',
          icon: 'none',
        });
        return;
      }
      let url = '';
      if (item.IsFill) {
        url =
          `./historyGaugeList?BaseGaugeId=` +
          item.ContentId +
          `&ReportId=${this.reportId}` +
          `&FillType=` +
          item.FillType;
      } else {
        url = `./screeningGauge?Id=` + item.ContentId;
      }
      uni.navigateTo({
        url,
      });
    },
    onSeeReport() {
      if (
        this.list.some((s) => s.IsFill) ||
        this.cognitiveTestingSystem.presence ||
        this.cognitiveTestingSystem.gait
      ) {
        let url = `./gaugeResult?reportId=${this.reportId}`;
        if (this.programId && this.programId !== 'undefined') {
          url += `&programId=${this.programId}`;
        }
        if (this.consultId && this.consultId !== 'undefined') {
          url += `&consultId=${this.consultId}`;
        }
        uni.navigateTo({
          url,
        });
      } else {
        uni.showToast({
          title: '请先完成评估',
          icon: 'none',
        });
      }
    },
    handleRefreshList(item) {
      const allGaugeList = [item];
      allGaugeList.forEach((v) => {
        v.Questions.forEach((s) => {
          if ((s.Type === 1 || s.Type === 0) && s.Answer) {
            s.Answer = JSON.stringify(s.Answer);
          }
          if (s.Type === 3 && s.Answer !== null) {
            s.Answer = String(s.Answer);
          }
        });
        v.UserId = app.globalData.userInfo.Id;
        v.CreatorId = app.globalData.userInfo.Id;
        v.ReportId = this.reportId;
        if (v.Id) {
          v.BaseGaugeId = v.Id;
          delete v.Id;
        }
      });
      // 发送请求
      uni.showLoading({
        title: '提交中',
        mask: true,
      });
      riskWarningWriteGauge(allGaugeList)
        .then((res) => {
          if (res.Type === 200 && res.Data) {
            uni.showToast({
              title: '提交成功',
              icon: 'none',
            });
            uni.hideLoading();
            this.onGetList();
          } else {
            uni.showModal({
              title: '温馨提示',
              content: res.Content,
              showCancel: false,
            });
          }
        })
        .finally(() => {
          uni.hideLoading();
        });
    },
  },
  onUnload() {
    const allGaugeIds = this.list.map((s) => s.ContentId);
    allGaugeIds.forEach((s) => {
      uni.removeStorageSync(s);
    });
    uni.$off(AssessClientEvent.updateRiskAssess, this.handleRefreshList);
  },
};
</script>

<style lang="scss" scoped>
.container {
  height: 100vh;
  background-color: #f7f7f7;
  padding: 30rpx;

  /deep/ .insideStyle {
    padding: 0 32rpx;
  }
  &-item {
    width: 100%;
    height: 190rpx;
    background: #ffffff;
    border-radius: 12rpx 12rpx 12rpx 12rpx;
    padding: 58rpx 22rpx;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 20rpx;
    &-image {
      width: 76rpx;
      height: 76rpx;
    }
    &-info {
      flex: 1;
      margin-left: 24rpx;
      &-top {
        font-size: 31rpx;
        color: #323233;
        line-height: 34rpx;
        font-weight: 600;
      }
      &-bom {
        font-weight: 400;
        font-size: 23rpx;
        color: rgba(50, 50, 51, 0.8);
        line-height: 34rpx;
        display: block;
      }
    }
    &-test {
      height: 46rpx;
      line-height: 46rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #ffffff;
      line-height: 34rpx;
      background: #0082f4;
      border-radius: 24rpx 24rpx 24rpx 24rpx;
      padding: 6rpx 16rpx;
    }
  }

  .box {
    padding: 20upx;
    background-color: white;
    margin-bottom: 35rpx;
    box-shadow: 2.5px 3px 9px 0px rgba(0, 0, 0, 0.08);
    border-radius: 16rpx;

    .box-top-right-top1 {
      margin-top: 20upx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .botton-style {
        width: 138upx;
        height: 60upx;
        background-color: #29b7a3;
        font-size: 12px;
        text-align: center;
        line-height: 60upx;
        border-radius: 30px;
        color: white;
      }
    }

    .box-top {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .box-top-right {
        flex: 1;
        margin-left: 20upx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .box-top-right-top {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .botton-style {
            width: 138upx;
            height: 60upx;
            background-color: #29b7a3;
            font-size: 15px;
            text-align: center;
            line-height: 60upx;
            border-radius: 30px;
          }
        }
      }
    }
  }
}
</style>
